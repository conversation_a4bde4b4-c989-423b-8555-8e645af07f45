<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena Core - Desktop Companion</title>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/black-theme.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <style>
    .arena-core-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      margin-top: 80px;
      text-align: center;
    }
    
    .hero-section {
      background: linear-gradient(135deg, #2c3e50, #34495e);
      border-radius: 12px;
      padding: 40px;
      margin-bottom: 30px;
      color: white;
    }
    
    .hero-title {
      font-size: 2.5em;
      font-weight: 700;
      margin-bottom: 15px;
      color: #f39c12;
    }
    
    .hero-subtitle {
      font-size: 1.2em;
      color: #bdc3c7;
      margin-bottom: 30px;
    }
    
    .download-section {
      background: linear-gradient(135deg, #8e44ad, #9b59b6);
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      color: white;
      margin: 30px 0;
    }
    
    .download-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 20px;
    }
    
    .download-description {
      font-size: 1.1em;
      margin-bottom: 30px;
      color: #ecf0f1;
    }
    
    .download-btn {
      display: inline-block;
      background: linear-gradient(135deg, #f39c12, #e67e22);
      color: white;
      padding: 18px 40px;
      border-radius: 30px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.2em;
      margin: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .download-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    .features-section {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 40px;
      margin: 30px 0;
    }
    
    .features-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 30px;
      color: #ecf0f1;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 25px;
      margin: 30px 0;
    }
    
    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 30px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
    
    .feature-icon {
      font-size: 3em;
      margin-bottom: 20px;
      color: #f39c12;
    }
    
    .feature-title {
      font-size: 1.4em;
      font-weight: 600;
      margin-bottom: 15px;
      color: #ecf0f1;
    }
    
    .feature-description {
      color: #bdc3c7;
      line-height: 1.6;
      font-size: 1.1em;
    }
    
    .cta-section {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      color: white;
      margin: 30px 0;
    }
    
    .cta-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .cta-description {
      font-size: 1.1em;
      margin-bottom: 25px;
      opacity: 0.9;
    }
    
    .cta-btn {
      display: inline-block;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 15px 30px;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1em;
      border: 2px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }
    
    .cta-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    /* Electron Dashboard Styles */
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 40px 0;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-icon {
      font-size: 2.5em;
      color: #ffd700;
      margin-bottom: 10px;
    }

    .stat-info h3 {
      font-size: 2em;
      margin: 0;
      color: white;
    }

    .stat-info p {
      margin: 5px 0 0 0;
      opacity: 0.8;
    }

    .games-section, .quick-actions-section {
      margin: 40px 0;
    }

    .games-section h2, .quick-actions-section h2 {
      color: #ffd700;
      margin-bottom: 20px;
      text-align: center;
    }

    .games-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }

    .game-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .game-card h3 {
      color: #ffd700;
      margin: 0 0 10px 0;
    }

    .game-card p {
      margin: 5px 0;
      opacity: 0.8;
    }

    .action-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1em;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .action-btn i {
      margin-right: 10px;
    }

    .loading {
      text-align: center;
      padding: 40px;
      opacity: 0.7;
    }

    /* Hide web-only sections in electron environment */
    .electron-app .web-only {
      display: none !important;
    }
  </style>
</head>
<body>
  <!-- Navbar will be loaded here -->
  <div id="navbar-container"></div>

  <div class="arena-core-container">
    <!-- Web Version (Download Page) -->
    <div id="web-content" class="web-content">
      <!-- Hero Section -->
      <div class="hero-section">
        <h1 class="hero-title">WC Arena Core</h1>
        <p class="hero-subtitle">The Ultimate Desktop Companion for Warcraft Players</p>
      </div>

      <!-- Download Section -->
      <div class="download-section">
        <h2 class="download-title">Download WC Arena Core</h2>
        <p class="download-description">
          Get the full desktop experience with game detection, screenshot management, and seamless integration with WC Arena.
        </p>
        <a href="/views/download-launcher.html" class="download-btn">
          <i class="fas fa-download"></i>
          Download Now
        </a>
      </div>
    </div>

    <!-- Electron Version (Dashboard) -->
    <div id="electron-content" class="electron-content" style="display: none;">
      <!-- Dashboard Header -->
      <div class="hero-section">
        <h1 class="hero-title">WC Arena Core Dashboard</h1>
        <p class="hero-subtitle">Your Desktop Companion is Active</p>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon"><i class="fas fa-gamepad"></i></div>
          <div class="stat-info">
            <h3 id="games-detected">0</h3>
            <p>Games Detected</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fas fa-camera"></i></div>
          <div class="stat-info">
            <h3 id="screenshots-taken">0</h3>
            <p>Screenshots Taken</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fas fa-trophy"></i></div>
          <div class="stat-info">
            <h3 id="matches-reported">0</h3>
            <p>Matches Reported</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fas fa-clock"></i></div>
          <div class="stat-info">
            <h3 id="uptime-hours">0</h3>
            <p>Hours Active</p>
          </div>
        </div>
      </div>

      <!-- Games Section -->
      <div class="games-section">
        <h2>Detected Games</h2>
        <div id="games-list" class="games-list">
          <div class="loading">Loading games...</div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions-section">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
          <button class="action-btn" onclick="openScreenshotsFolder()">
            <i class="fas fa-folder-open"></i>
            Open Screenshots Folder
          </button>
          <button class="action-btn" onclick="refreshGameDetection()">
            <i class="fas fa-sync"></i>
            Refresh Game Detection
          </button>
          <button class="action-btn" onclick="openSettings()">
            <i class="fas fa-cog"></i>
            Settings
          </button>
        </div>
      </div>
    </div>

    <!-- Key Features Section (Web Only) -->
    <div class="features-section web-only">
      <h2 class="features-title">Key Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-gamepad"></i>
          </div>
          <h3 class="feature-title">Game Manager</h3>
          <p class="feature-description">
            Automatically detect and launch your Warcraft games. Track game sessions, manage multiple game versions, and get instant access to your favorite titles.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-camera"></i>
          </div>
          <h3 class="feature-title">Screenshot Manager</h3>
          <p class="feature-description">
            Intelligent screenshot organization and analysis. Automatically categorize screenshots by game, detect victories and defeats, and keep your gaming memories organized.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <h3 class="feature-title">Match Tracking</h3>
          <p class="feature-description">
            Track your match results automatically. Get detailed statistics, performance insights, and share your achievements with the community.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3 class="feature-title">Community Integration</h3>
          <p class="feature-description">
            Seamlessly connect with the WC Arena community. Share screenshots, track leaderboards, and stay updated with the latest news and events.
          </p>
        </div>
      </div>
    </div>

    <!-- Call to Action (Web Only) -->
    <div class="cta-section web-only">
      <h2 class="cta-title">Ready to Enhance Your Warcraft Experience?</h2>
      <p class="cta-description">
        Join thousands of players who have already upgraded their gaming setup with WC Arena Core.
      </p>
      <a href="/views/download-launcher.html" class="cta-btn">
        <i class="fas fa-rocket"></i>
        Get Started Now
      </a>
    </div>
  </div>

  <!-- Scripts -->
  <script src="/js/utils.js"></script>
  <script src="/js/main.js"></script>
  
  <script>
    // Detect Electron environment
    function isElectronEnvironment() {
      const urlParams = new URLSearchParams(window.location.search);
      return !!(window.electronAPI ||
                navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
                urlParams.get('electron') === 'true' ||
                urlParams.get('electronApp'));
    }

    // Show appropriate content based on environment
    function showAppropriateContent() {
      const isElectron = isElectronEnvironment();
      const webContent = document.getElementById('web-content');
      const electronContent = document.getElementById('electron-content');

      console.log('🔍 Environment detection:', { isElectron });

      if (isElectron) {
        webContent.style.display = 'none';
        electronContent.style.display = 'block';
        loadElectronDashboard();
      } else {
        webContent.style.display = 'block';
        electronContent.style.display = 'none';
      }
    }

    // Load Electron dashboard data
    async function loadElectronDashboard() {
      console.log('🖥️ Loading Electron dashboard...');

      try {
        if (window.electronAPI && window.electronAPI.arenaCore) {
          const response = await window.electronAPI.arenaCore.requestData();
          console.log('📊 Arena Core data:', response);

          if (response.success) {
            updateDashboardStats(response.data.stats);
            updateGamesList(response.data.games);
          }
        } else {
          console.warn('⚠️ Electron API not available, showing placeholder data');
          updateDashboardStats({
            gamesDetected: 0,
            screenshotsTaken: 0,
            matchesReported: 0,
            uptimeHours: 0
          });
          updateGamesList([]);
        }
      } catch (error) {
        console.error('❌ Failed to load dashboard data:', error);
      }
    }

    // Update dashboard statistics
    function updateDashboardStats(stats) {
      document.getElementById('games-detected').textContent = stats.gamesDetected || 0;
      document.getElementById('screenshots-taken').textContent = stats.screenshotsTaken || 0;
      document.getElementById('matches-reported').textContent = stats.matchesReported || 0;
      document.getElementById('uptime-hours').textContent = stats.uptimeHours || 0;
    }

    // Update games list
    function updateGamesList(games) {
      const gamesList = document.getElementById('games-list');

      if (!games || games.length === 0) {
        gamesList.innerHTML = '<div class="loading">No games detected. Make sure your games are installed and try refreshing.</div>';
        return;
      }

      gamesList.innerHTML = games.map(game => `
        <div class="game-card">
          <h3>${game.name || game.type}</h3>
          <p><strong>Type:</strong> ${game.type}</p>
          <p><strong>Path:</strong> ${game.path}</p>
          <p><strong>Status:</strong> ${game.isRunning ? 'Running' : 'Not Running'}</p>
        </div>
      `).join('');
    }

    // Action button functions
    function openScreenshotsFolder() {
      if (window.electronAPI && window.electronAPI.screenshots) {
        window.electronAPI.screenshots.openFolder();
      }
    }

    function refreshGameDetection() {
      if (window.electronAPI && window.electronAPI.games) {
        window.electronAPI.games.refresh();
        setTimeout(loadElectronDashboard, 1000); // Reload dashboard after refresh
      }
    }

    function openSettings() {
      // Navigate to settings page or open settings modal
      console.log('Opening settings...');
    }

    // Wait for app to be ready before loading navbar
    document.addEventListener('DOMContentLoaded', async function() {
      // Show appropriate content first
      showAppropriateContent();

      // For electron environment, wait for proper initialization
      const isElectron = isElectronEnvironment();
      if (isElectron) {
        console.log('🖥️ Electron environment detected, waiting for proper initialization...');

        // Wait for app initialization to complete
        await new Promise(resolve => {
          const checkAppReady = () => {
            if (window._appInstance && window.apiClient) {
              console.log('✅ App ready, loading navbar');
              resolve();
            } else {
              console.log('⏳ Waiting for app to be ready...');
              setTimeout(checkAppReady, 100);
            }
          };
          checkAppReady();
        });

        // Wait for auth manager to be ready and user data loaded
        await new Promise(resolve => {
          let attempts = 0;
          const maxAttempts = 100; // 10 seconds max wait

          const checkAuthReady = () => {
            attempts++;

            // Check if auth manager exists and has loaded user data
            if (window._appInstance && window._appInstance.getComponent('auth')) {
              const authManager = window._appInstance.getComponent('auth');
              if (authManager.initialized && authManager.getUser()) {
                console.log('✅ Auth manager ready, user data loaded:', authManager.getUser().username);
                resolve();
                return;
              }
            }

            // Also check if we have user data in localStorage (fallback)
            const storedToken = localStorage.getItem('authToken');
            if (storedToken && attempts > 20) {
              console.log('⚠️ Using fallback - found stored auth token, proceeding...');
              resolve();
              return;
            }

            if (attempts >= maxAttempts) {
              console.warn('⚠️ Auth manager timeout, proceeding anyway...');
              resolve();
              return;
            }

            console.log(`⏳ Waiting for auth manager to initialize and load user... (${attempts}/${maxAttempts})`);
            setTimeout(checkAuthReady, 100);
          };
          checkAuthReady();
        });

        // Load user data to ensure navbar is properly updated
        if (window.loadUser && typeof window.loadUser === 'function') {
          await window.loadUser();
        }
      }

      try {
        console.log('🔄 Initializing navigation on arena-core page...');

        // Load unified navigation
        if (typeof window.loadNavigation === 'function') {
          await window.loadNavigation();
        } else if (typeof window.loadNavbar === 'function') {
          await window.loadNavbar();
        } else {
          // Fallback to manual loading
          const navbarResponse = await fetch('/components/navbar.html');
          if (navbarResponse.ok) {
            const navbarHtml = await navbarResponse.text();
            document.getElementById('navbar-container').innerHTML = navbarHtml;
            console.log('✅ Navbar HTML loaded successfully');

            const script = document.createElement('script');
            script.src = '/js/unified-navigation.js';
            script.onload = function() {
              console.log('✅ Unified navigation script loaded, initializing...');
              if (window.initUnifiedNavigation) {
                window.initUnifiedNavigation();
              }
            };
            document.head.appendChild(script);
          }
        }

        // Update navbar profile with proper user data - multiple attempts
        const updateNavbarWithRetry = async (attempt = 1, maxAttempts = 5) => {
          console.log(`🔄 Updating navbar profile attempt ${attempt}/${maxAttempts}`);

          // Ensure we have user data before updating navbar
          let userData = null;
          if (window._appInstance) {
            const authManager = window._appInstance.getComponent('auth');
            if (authManager && authManager.getUser()) {
              userData = authManager.getUser();
              console.log('✅ Using user data from AuthManager for navbar:', userData.username);
            }
          }

          // Fallback: try to get user data from API if not available from AuthManager
          if (!userData && window.apiClient) {
            try {
              const response = await window.apiClient.get('/api/me');
              if (response && response.username) {
                userData = response;
                console.log('✅ Got user data from API fallback:', userData.username);
              }
            } catch (error) {
              console.warn('⚠️ Could not get user data from API:', error);
            }
          }

          if (window.updateNavbarProfileUnified) {
            console.log('🔄 Updating navbar profile (unified) on arena-core page');
            await window.updateNavbarProfileUnified();

            // Force update with user data if available
            if (userData && window.unifiedNavigation) {
              console.log('🔄 Force updating navbar with user data:', userData.username);
              window.unifiedNavigation.updateUser(userData);
            }
          } else if (window.updateNavbarProfile) {
            console.log('🔄 Updating navbar profile (legacy) on arena-core page');
            await window.updateNavbarProfile();
          }

          // Check if navbar was successfully updated
          const usernameElement = document.querySelector('#navbar-username');
          if (usernameElement && usernameElement.textContent === 'Loading...' && attempt < maxAttempts) {
            console.log(`⚠️ Navbar still showing 'Loading...', retrying in 1 second (attempt ${attempt + 1})`);
            setTimeout(() => updateNavbarWithRetry(attempt + 1, maxAttempts), 1000);
          } else if (usernameElement) {
            console.log('✅ Navbar username updated to:', usernameElement.textContent);
          }
        };

        setTimeout(() => updateNavbarWithRetry(), 500);

      } catch (error) {
        console.warn('Could not load navigation:', error);
      }
    });
  </script>
</body>
</html> 