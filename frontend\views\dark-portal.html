<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dark Portal - Community Hub</title>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/black-theme.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <style>
    .dark-portal-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      margin-top: 80px;
    }
    
    .portal-hero {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .portal-title {
      font-family: 'Cinzel', serif;
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 800;
      background: linear-gradient(45deg, #8B0000, #FF4500, #8B0000);
      background-size: 200% 200%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: portalPulse 3s ease-in-out infinite;
      margin-bottom: 1rem;
      text-shadow: 0 0 20px rgba(139, 0, 0, 0.5);
    }
    
    @keyframes portalPulse {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }
    
    .portal-subtitle {
      font-size: 1.2rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 2rem;
    }
    
    /* Game Type Tabs */
    .game-tabs {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    }
    
    .game-tab {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.8);
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .game-tab:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: var(--primary-gold);
      color: var(--primary-gold);
      transform: translateY(-2px);
    }
    
    .game-tab.active {
      background: var(--primary-gold);
      border-color: var(--primary-gold);
      color: #000;
      box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    }
    
    /* Community Sections */
    .community-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }
    
    .community-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 1.5rem;
      transition: all 0.3s ease;
    }
    
    .community-section:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;
      padding-bottom: 0.75rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .section-icon {
      font-size: 1.5rem;
      color: var(--primary-gold);
    }
    
    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #fff;
      margin: 0;
    }
    
    .links-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .community-link {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      text-decoration: none;
      color: rgba(255, 255, 255, 0.9);
      transition: all 0.3s ease;
    }
    
    .community-link:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--primary-gold);
      color: var(--primary-gold);
      transform: translateX(5px);
    }
    
    .link-icon {
      font-size: 1.1rem;
      width: 20px;
      text-align: center;
    }
    
    .link-content {
      flex: 1;
    }
    
    .link-title {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    .link-description {
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.4;
    }
    
    .external-icon {
      font-size: 0.8rem;
      opacity: 0.6;
    }
    
    /* Submit Link Section */
    .submit-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      margin-top: 2rem;
    }
    
    .submit-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #fff;
      margin-bottom: 1rem;
    }
    
    .submit-description {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    
    .submit-btn {
      background: linear-gradient(135deg, var(--primary-gold), #E5C158);
      color: #000;
      border: none;
      padding: 0.75rem 2rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
    }
    
    .loading {
      text-align: center;
      padding: 2rem;
      color: rgba(255, 255, 255, 0.6);
    }
    
    .no-links {
      text-align: center;
      padding: 2rem;
      color: rgba(255, 255, 255, 0.6);
      font-style: italic;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      .community-sections {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .game-tabs {
        gap: 0.5rem;
      }
      
      .game-tab {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
      }
      
      .community-section {
        padding: 1rem;
      }
    }
  </style>
</head>
<body>
  <!-- Navbar will be loaded here -->
  <div id="navbar-container"></div>

  <div class="dark-portal-container">
    <!-- Hero Section -->
    <div class="portal-hero">
      <h1 class="portal-title">🌀 Dark Portal</h1>
      <p class="portal-subtitle">Your gateway to the Warcraft community</p>
    </div>

    <!-- Game Type Tabs -->
    <div class="game-tabs">
      <button class="game-tab active" data-game="all">
        <i class="fas fa-globe"></i>
        All Games
      </button>
      <button class="game-tab" data-game="wc12">
        <i class="fas fa-shield-alt"></i>
        WC I/II
      </button>
      <button class="game-tab" data-game="warcraft3">
        <i class="fas fa-crown"></i>
        WC III
      </button>
    </div>

    <!-- Community Sections -->
    <div class="community-sections" id="community-sections">
      <!-- Sections will be loaded dynamically -->
    </div>

    <!-- Submit Link Section -->
    <div class="submit-section">
      <h2 class="submit-title">Know a Great Community Resource?</h2>
      <p class="submit-description">
        Help grow our community by submitting links to Reddit communities, Discord servers, 
        Battle.net groups, map/mod sites, and other Warcraft resources. All submissions are reviewed by our team.
      </p>
      <button class="submit-btn" onclick="showSubmitModal()">
        <i class="fas fa-plus"></i>
        Submit a Link
      </button>
    </div>
  </div>

  <!-- Footer will be loaded here -->
  <div id="footer-container"></div>

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script type="module" src="/js/app.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/dark-portal.js"></script>
</body>
</html>
