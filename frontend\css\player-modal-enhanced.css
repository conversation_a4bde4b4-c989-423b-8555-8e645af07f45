/* ==========================================================================
   PLAYER MODAL STYLING FIXES
   Focused on fixing spacing, visibility, and layout issues
   ========================================================================== */

/* ===== MODAL OVERLAY ===== */
#player-stats-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

#player-stats-modal.show {
  opacity: 1;
  visibility: visible;
}

/* ===== MODAL CONTENT ===== */
#player-stats-modal .modal-content {
  background: #1a1a1a;
  border-radius: 12px;
  border: 2px solid #D4AF37;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  max-width: 90vw;
  max-height: 90vh;
  width: 1200px;
  height: 90vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* ===== PLAYER MODAL CONTAINER ===== */
.player-modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  overflow: hidden;
}

/* ===== PLAYER HEADER - FIXED SPACING ===== */
.player-modal-header {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a, #1a1a1a);
  border-bottom: 3px solid #D4AF37;
  padding: 24px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 100px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  gap: 20px;
}

.player-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.05) 50%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.player-info {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  padding: 8px 0;
  flex-direction: row;
  flex-wrap: nowrap;
}

.player-rank {
  width: 72px;
  height: 72px;
  border-radius: 12px;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border: 3px solid #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
}

.player-rank::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: left 0.6s;
}

.player-rank:hover::before {
  left: 100%;
}

.player-rank .rank-icon {
  width: 58px;
  height: 58px;
  object-fit: contain;
  border-radius: 6px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.player-rank:hover .rank-icon {
  transform: scale(1.05);
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-width: 0;
  justify-content: center;
}

.player-name {
  font-size: 2rem;
  font-weight: 700;
  color: #D4AF37;
  margin: 0;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-family: 'Cinzel', serif;
  letter-spacing: 0.5px;
}

.player-race-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.race-display {
  font-size: 0.9rem;
  color: #e0e0e0;
  font-weight: 500;
  padding: 6px 12px;
  background: linear-gradient(135deg, #333, #2a2a2a);
  border-radius: 20px;
  border: 1px solid #555;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.race-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.4s;
}

.race-display:hover::before {
  left: 100%;
}

.player-rank-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rank-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #D4AF37;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 4px 12px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
  border-radius: 15px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.1);
}

.player-mmr {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-radius: 12px;
  border: 2px solid #D4AF37;
  min-width: 90px;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
  position: relative;
  overflow: hidden;
}

.player-mmr::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.5s;
}

.player-mmr:hover::before {
  left: 100%;
}

.mmr-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #D4AF37;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Cinzel', serif;
}

.mmr-label {
  font-size: 0.75rem;
  color: #ccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

/* ===== CLOSE BUTTON - FIXED POSITIONING ===== */
.close-modal {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #333;
  border: 1px solid #D4AF37;
  color: #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  z-index: 10;
  flex-shrink: 0;
}

.close-modal:hover {
  background: #D4AF37;
  color: #1a1a1a;
  transform: scale(1.1);
}

/* ===== MODAL TABS ===== */
.modal-tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #D4AF37;
}

.modal-tab {
  padding: 15px 25px;
  background: transparent;
  border: none;
  color: #ccc;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.modal-tab:hover {
  color: #D4AF37;
  background: #333;
}

.modal-tab.active {
  color: #D4AF37;
  border-bottom-color: #D4AF37;
  background: #333;
}

/* ===== TAB CONTENT ===== */
.modal-tab-content {
  display: none;
  flex: 1;
  overflow: hidden;
  padding: 0;
  min-height: 0;
}

.modal-tab-content.active {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== OVERVIEW CONTENT ===== */
.overview-content {
  padding: 30px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #2a2a2a;
  border-radius: 10px;
  border: 1px solid #D4AF37;
  padding: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.2);
}

.stat-content {
  text-align: center;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #D4AF37;
  margin-bottom: 8px;
}

.stat-value.positive {
  color: #4ade80;
}

.stat-value.negative {
  color: #f87171;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== MATCHES CONTENT ===== */
.matches-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  height: 100%;
}

.matches-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 30px;
  min-height: 0;
  height: 100%;
}

/* ===== MATCH ITEM - FIXED VISIBILITY ===== */
.match-item {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-radius: 12px;
  border: 2px solid #444;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
}

.match-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.02) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.match-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border-color: #D4AF37;
}

.match-item:hover::before {
  opacity: 1;
}

.match-item.match-win {
  border-left: 4px solid #4ade80;
}

.match-item.match-loss {
  border-left: 4px solid #f87171;
}

.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 20px;
  background: linear-gradient(135deg, #333, #2a2a2a);
  position: relative;
  z-index: 1;
  min-height: 60px;
  border-bottom: 1px solid #444;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  gap: 16px;
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 1.1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.match-win .match-outcome {
  color: #4ade80;
}

.match-loss .match-outcome {
  color: #f87171;
}

.outcome-text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 700;
  font-size: 1.1rem;
}

.match-type {
  font-size: 1rem;
  color: #D4AF37;
  font-weight: 700;
  background: rgba(212, 175, 55, 0.15);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.match-date {
  font-size: 0.95rem;
  color: #e0e0e0;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.08);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.match-expand-icon {
  color: #ccc;
  transition: all 0.3s ease;
}

.match-item:hover .match-expand-icon {
  color: #D4AF37;
}

/* MMR Change styling for match headers */
.mmr-change {
  font-size: 1rem;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 4px;
}

.mmr-change.positive {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.15);
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.mmr-change.negative {
  color: #f87171;
  background: rgba(248, 113, 113, 0.15);
  border: 1px solid rgba(248, 113, 113, 0.3);
}

.mmr-change i {
  font-size: 0.9rem;
}

/* Match details section removed - now handled by modal */

/* Match map and players container removed - now handled by modal */

/* Match players and team styles removed - now handled by modal */

/* Player link and MMR change styles removed - now handled by modal */

/* ===== EXPANDED MATCH DETAILS - FIXED VISIBILITY ===== */
.match-details-expanded {
  background: #333;
  border-top: 1px solid #444;
  padding: 20px;
}

.match-details-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.match-details-section {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #444;
}

.match-details-section h4 {
  color: #D4AF37;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.match-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-detail {
  background: #333;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #444;
}

.team-detail.winning-team {
  border-color: rgba(74, 222, 128, 0.5);
  background: rgba(74, 222, 128, 0.05);
}

.team-detail.losing-team {
  border-color: rgba(248, 113, 113, 0.5);
  background: rgba(248, 113, 113, 0.05);
}

.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.team-label {
  color: #ccc;
}

.team-result.winner {
  color: #4ade80;
}

.team-result.loser {
  color: #f87171;
}

.team-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.player-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2a2a2a;
  border-radius: 4px;
  font-size: 0.9rem;
}

.player-detail.current-player {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.player-detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.player-detail-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ccc;
  font-size: 0.8rem;
}

.result-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-badge.winner {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.result-badge.loser {
  background: rgba(248, 113, 113, 0.2);
  color: #f87171;
  border: 1px solid rgba(248, 113, 113, 0.3);
}

.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.match-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #333;
  border-radius: 4px;
  font-size: 0.9rem;
}

.info-label {
  color: #ccc;
  font-weight: 500;
}

.info-value {
  color: #fff;
  font-weight: 600;
}

.info-value.positive {
  color: #4ade80;
}

.info-value.negative {
  color: #f87171;
}

/* ===== PAGINATION ===== */
.matches-pagination {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-top: 2px solid #D4AF37;
  padding: 20px 30px;
  box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #ccc;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  margin-bottom: 0;
}

.matches-range,
.page-info {
  font-weight: 500;
  color: #e0e0e0;
}

.matches-range {
  color: #D4AF37;
  font-weight: 600;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.btn {
  padding: 10px 18px;
  border-radius: 8px;
  border: 2px solid #D4AF37;
  background: linear-gradient(135deg, #333, #2a2a2a);
  color: #D4AF37;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.4s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  color: #1a1a1a;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #333, #2a2a2a);
  border-color: #666;
  color: #ccc;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 8px;
}

.page-ellipsis {
  color: #999;
  padding: 0 8px;
  font-weight: 600;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  #player-stats-modal .modal-content {
    width: 95vw;
    max-height: 95vh;
  }
  
  .player-modal-header {
    padding: 20px 25px;
    min-height: 90px;
  }
  
  .player-info {
    gap: 20px;
  }
  
  .player-rank {
    width: 65px;
    height: 65px;
  }
  
  .player-rank .rank-icon {
    width: 55px;
    height: 55px;
  }
  
  .player-name {
    font-size: 1.7rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
  }
  
  .stat-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .player-modal-header {
    flex-direction: row;
    gap: 15px;
    text-align: left;
    padding: 15px;
    min-height: auto;
  }
  
  .player-info {
    flex-direction: row;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .player-rank {
    width: 60px;
    height: 60px;
  }
  
  .player-rank .rank-icon {
    width: 50px;
    height: 50px;
  }
  
  .player-name {
    font-size: 1.5rem;
    text-align: left;
  }
  
  .player-details {
    align-items: flex-start;
    text-align: left;
    flex: 1;
  }
  
  .player-mmr {
    min-width: 80px;
    padding: 12px 16px;
  }
  
  .mmr-value {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .player-modal-header {
    padding: 12px 15px;
    gap: 12px;
  }
  
  .player-info {
    gap: 12px;
  }
  
  .player-rank {
    width: 50px;
    height: 50px;
  }
  
  .player-rank .rank-icon {
    width: 42px;
    height: 42px;
  }
  
  .player-name {
    font-size: 1.3rem;
  }
  
  .player-details {
    gap: 8px;
  }
  
  .race-display {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
  
  .rank-name {
    font-size: 0.9rem;
    padding: 3px 8px;
  }
  
  .player-mmr {
    min-width: 70px;
    padding: 10px 12px;
  }
  
  .mmr-value {
    font-size: 1.1rem;
  }
  
  .mmr-label {
    font-size: 0.7rem;
  }
  
  .modal-tabs {
    padding: 0 10px;
  }
  
  .modal-tab {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .overview-content,
  .matches-list {
    padding: 15px;
  }
  
  .match-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .match-details {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .match-players {
    flex-direction: column;
    gap: 8px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .match-info-grid {
    grid-template-columns: 1fr;
  }
  
  .matches-pagination {
    padding: 15px 20px;
  }
  
  .pagination-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    align-items: center;
  }
  
  .matches-range,
  .page-info {
    font-size: 0.8rem;
  }
  
  .pagination-controls {
    gap: 6px;
  }
  
  .btn {
    padding: 8px 14px;
    font-size: 0.8rem;
  }
}

/* ===== SCROLLBAR STYLING ===== */
.matches-list::-webkit-scrollbar {
  width: 8px;
}

.matches-list::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

.matches-list::-webkit-scrollbar-thumb {
  background: #D4AF37;
  border-radius: 4px;
}

.matches-list::-webkit-scrollbar-thumb:hover {
  background: #b8941f;
}

/* ===== LOADING STATE ===== */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #ccc;
  font-style: italic;
} 