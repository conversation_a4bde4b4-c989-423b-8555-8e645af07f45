<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://js-sandbox.squarecdn.com https://js.squarecdn.com https://js.afterpay.com https://js-sandbox.afterpay.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://sandbox.web.squarecdn.com https://web.squarecdn.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com https://square-fonts-production-f.squarecdn.com https://d1g145x70srn7h.cloudfront.net; img-src 'self' data: https:; connect-src 'self' http://127.0.0.1:3001 http://localhost:3001 https://*.googleapis.com https://*.google.com https://connect.squareupsandbox.com https://connect.squareup.com https://pci-connect.squareupsandbox.com https://pci-connect.squareup.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk; frame-src 'self' https://www.youtube.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk;">
  <title>WC Arena - Ladder</title>
  
  <!-- Preload critical fonts -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- CSS Dependencies -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/ladder-consolidated.css" />
  <link rel="stylesheet" href="/css/profile-specific.css" />
  <link rel="stylesheet" href="/css/rank-animation.css" />
  <link rel="stylesheet" href="/css/matches-tab-enhanced.css" />
  <link rel="stylesheet" href="/css/player-modal-enhanced.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <!-- Chart.js - Single version -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- Custom CSS for Ladder Page -->
  <style>
    /* Override ladder-header to center content */
    .ladder-header {
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      margin: 0.5rem 0 1rem 0 !important;
      gap: 1rem !important;
    }
    
    .tabs-and-button-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      width: 100%;
    }
    
    /* Game Tabs Container - Override existing styles */
    .game-tabs-container {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    
    .game-tabs-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;
      flex-wrap: wrap;
    }
    
    .game-tabs {
      display: flex;
      gap: 0.4rem;
      justify-content: center;
      flex: 1;
    }
    
    .game-tab {
      position: relative;
      display: flex;
      align-items: center;
      gap: 0.3rem;
      padding: 0.4rem 0.8rem;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      color: rgba(255, 255, 255, 0.7);
      font-family: 'Cinzel', serif;
      font-size: 0.7rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      min-width: 100px;
      justify-content: center;
    }
    
    .game-tab::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
      transition: left 0.6s;
    }
    
    .game-tab:hover {
      background: rgba(255, 215, 0, 0.08);
      border-color: rgba(255, 215, 0, 0.2);
      color: #ffd700;
      transform: translateY(-1px) scale(1.02);
      box-shadow: 0 3px 10px rgba(255, 215, 0, 0.2);
    }
    
    .game-tab:hover::before {
      left: 100%;
    }
    
    .game-tab.active {
      background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 237, 78, 0.15));
      border-color: #ffd700;
      color: #ffd700;
      transform: translateY(-1px) scale(1.02);
      box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
    }
    
    .game-tab i {
      font-size: 0.9rem;
    }
    
    .game-tab span {
      font-size: 0.7rem;
    }
    
    /* Game specific styling */
    .game-tab[data-game="war1"] {
      border-color: rgba(139, 69, 19, 0.5);
    }
    
    .game-tab[data-game="war1"]:hover,
    .game-tab[data-game="war1"].active {
      border-color: #8b4513;
      color: #daa520;
      background: linear-gradient(45deg, rgba(139, 69, 19, 0.2), rgba(218, 165, 32, 0.2));
    }
    
    .game-tab[data-game="war3"] {
      border-color: rgba(138, 43, 226, 0.5);
    }
    
    .game-tab[data-game="war3"]:hover,
    .game-tab[data-game="war3"].active {
      border-color: #8a2be2;
      color: #da70d6;
      background: linear-gradient(45deg, rgba(138, 43, 226, 0.2), rgba(218, 112, 214, 0.2));
    }
    
    /* Report Match Section */
    .report-match-section {
      text-align: center !important;
      margin: 0 !important;
      width: 100% !important;
    }
    
    .btn-report {
      background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
      border: none !important;
      color: #0f172a !important;
      padding: 1rem 2rem !important;
      border-radius: 10px !important;
      font-weight: 600 !important;
      cursor: pointer !important;
      transition: all 0.3s ease !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 0.5rem !important;
      font-family: 'Cinzel', serif !important;
      font-size: 1rem !important;
      text-transform: uppercase !important;
      letter-spacing: 1px !important;
    }
    
    .btn-report:hover {
      background: linear-gradient(45deg, #ffed4e, #ffd700) !important;
    }
    
    /* Ladder content spacing */
    .ladder-layout {
      margin-top: 1rem !important;
    }
    
    /* Reduce page header bottom margin for tighter spacing */
    .page-header {
      margin-bottom: 0.5rem !important;
    }
    
    /* Responsive design for tabs */
    @media (max-width: 768px) {
      .game-tabs-wrapper {
        flex-direction: column;
        gap: 1rem;
      }
      
      .game-tabs {
        width: 100%;
        justify-content: center;
      }
      
      .game-tab {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
      }
      
      .game-tab span {
        font-size: 0.8rem;
      }
      
      .btn-report {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
      }
    }

    /* ===== WC1 UNIT SELECTION STYLING ===== */
    .unit-instructions {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: rgba(255, 193, 7, 0.1);
      border: 1px solid rgba(255, 193, 7, 0.3);
      border-radius: 8px;
      text-align: center;
    }

    .units-container {
      margin-bottom: 2rem;
    }

    .units-container h5 {
      font-family: 'Cinzel', serif;
      font-size: 1.2rem;
      color: #ffd700;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .units-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.02);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 10px;
    }

    .unit-selector {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .unit-selector:hover {
      background: rgba(255, 215, 0, 0.08);
      border-color: rgba(255, 215, 0, 0.3);
    }

    .unit-selector label {
      font-family: 'Cinzel', serif;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .unit-selector label i {
      color: #ffd700;
      width: 16px;
      text-align: center;
    }

    .unit-slider-container {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .unit-slider {
      flex: 1;
      -webkit-appearance: none;
      appearance: none;
      height: 6px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      outline: none;
      transition: all 0.3s ease;
    }

    .unit-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .unit-slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .unit-slider::-webkit-slider-thumb:hover {
      transform: scale(1.2);
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }

    .unit-slider::-moz-range-thumb:hover {
      transform: scale(1.2);
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }

    .unit-count {
      min-width: 40px;
      text-align: center;
      font-family: 'Cinzel', serif;
      font-weight: 700;
      font-size: 1rem;
      color: #ffd700;
      background: rgba(255, 215, 0, 0.1);
      border: 1px solid rgba(255, 215, 0, 0.3);
      border-radius: 6px;
      padding: 0.3rem 0.5rem;
    }

    .unit-count[data-value="0"] {
      color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }

    .unit-validation {
      margin-top: 1rem;
      padding: 1rem;
      background: rgba(220, 53, 69, 0.1);
      border: 1px solid rgba(220, 53, 69, 0.3);
      border-radius: 8px;
    }

    .validation-message {
      color: #dc3545;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    /* Unit count animation when changed */
    .unit-count.changed {
      animation: unitCountPulse 0.3s ease;
    }

    @keyframes unitCountPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.2); }
      100% { transform: scale(1); }
    }

    /* Race-specific styling */
    #human-units .unit-slider::-webkit-slider-track,
    #human-units .unit-slider {
      background: linear-gradient(90deg, rgba(70, 130, 180, 0.2), rgba(70, 130, 180, 0.4));
    }

    #orc-units .unit-slider::-webkit-slider-track,
    #orc-units .unit-slider {
      background: linear-gradient(90deg, rgba(139, 69, 19, 0.2), rgba(139, 69, 19, 0.4));
    }

    /* Mobile responsive for unit selection */
    @media (max-width: 768px) {
      .units-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
      }
      
      .unit-selector {
        padding: 0.8rem;
      }
      
      .unit-selector label {
        font-size: 0.8rem;
      }
      
      .unit-count {
        min-width: 30px;
        font-size: 0.9rem;
        padding: 0.2rem 0.4rem;
      }
    }

    @media (max-width: 480px) {
      .units-grid {
        grid-template-columns: 1fr;
      }
      
      .unit-instructions {
        padding: 0.8rem;
        font-size: 0.8rem;
      }
    }

    /* ===== UNIT DISPLAY IN MATCH HISTORY ===== */
    .match-units {
      margin: 0.5rem 0;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.02);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
    }

    .units-label {
      font-family: 'Cinzel', serif;
      font-size: 0.8rem;
      color: #ffd700;
      font-weight: 600;
      display: block;
      margin-bottom: 0.3rem;
    }

    .units-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .unit-item {
      display: inline-flex;
      align-items: center;
      gap: 0.3rem;
      padding: 0.2rem 0.5rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      font-size: 0.75rem;
      color: rgba(255, 255, 255, 0.8);
      font-family: 'Cinzel', serif;
    }

    .unit-item i {
      color: #ffd700;
      font-size: 0.7rem;
    }

    /* Mobile responsive for unit display */
    @media (max-width: 768px) {
      .match-units {
        padding: 0.3rem;
      }
      
      .units-list {
        gap: 0.3rem;
      }
      
      .unit-item {
        padding: 0.15rem 0.4rem;
        font-size: 0.7rem;
      }
      
      .unit-item i {
        font-size: 0.65rem;
      }
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Page Title -->
    <div class="page-header">
      <h1 class="page-title" data-theme="live">🏆 Arena</h1>
    </div>

    <!-- Game Type Selection Area (for new users) -->
    <div id="game-type-selection-area" class="hidden">
      <!-- Game type selector will be loaded here if needed -->
    </div>

    <!-- Main Ladder Content -->
    <div id="ladder-main-content">
      <!-- Header Section -->
      <div class="ladder-header">
        <!-- Centered Tabs and Report Button -->
        <div class="tabs-and-button-container">
          <!-- Game Type Tabs (Ladder Style) -->
          <section class="game-tabs-container fade-in">
            <div class="game-tabs-wrapper">
              <div class="game-tabs">
                <button class="game-tab" data-game-type="war1" data-game="war1">
                  <i class="fas fa-dragon"></i>
                  <span>WC I</span>
                </button>
                <button class="game-tab active" data-game-type="war2" data-game="war2">
                  <i class="fas fa-shield-alt"></i>
                  <span>WC II</span>
                </button>
                <button class="game-tab" data-game-type="war3" data-game="war3">
                  <i class="fas fa-chess-king"></i>
                  <span>WC III</span>
                </button>
              </div>
            </div>
          </section>

          <!-- Report Match Button (Below Tabs) -->
          <div class="report-match-section">
            <button id="report-match-btn" class="btn btn-primary btn-report">
              <i class="fas fa-trophy"></i> 
              <span id="report-match-text">REPORT MATCH</span>
              <i class="fas fa-trophy"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- WC2/WC3 Search and Filters -->
      <div class="controls-section" id="wc2-wc3-controls">
        <!-- Player Search -->
        <div class="player-search">
          <div class="search-input-group">
            <input type="text" id="player-search" placeholder="Search player name...">
            <button id="search-btn" class="btn btn-search">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Map Search Filter -->
        <div class="map-search">
          <div class="search-input-group">
            <input type="text" id="map-search" placeholder="Search specific map...">
            <button id="map-search-btn" class="btn btn-search">
              <i class="fas fa-map"></i>
            </button>
          </div>
        </div>

        <!-- Match Type Filter -->
        <div class="match-type-filter">
          <div class="filter-buttons">
            <button class="filter-btn active" data-match-type="all">All</button>
            <button class="filter-btn" data-match-type="1v1">1v1</button>
            <button class="filter-btn" data-match-type="2v2">2v2</button>
            <button class="filter-btn" data-match-type="3v3">3v3</button>
            <button class="filter-btn" data-match-type="4v4">4v4</button>
            <button class="filter-btn" data-match-type="ffa">FFA</button>
            <button class="filter-btn" data-match-type="vsai">vs AI</button>
          </div>
        </div>
      </div>

      <!-- WC1 Search and Filters -->
      <div class="controls-section" id="wc1-controls" style="display: none;">
        <!-- Player Search -->
        <div class="player-search">
          <div class="search-input-group">
            <input type="text" id="wc1-player-search" placeholder="Search WC1 player name...">
            <button id="wc1-search-btn" class="btn btn-search">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <!-- WC1 Match Type Toggle -->
        <div class="match-type-filter">
          <div class="filter-buttons">
            <button class="filter-btn active" id="wc1-vsai-btn" data-match-type="vsai">
              <i class="fas fa-robot"></i>
              <span>vs AI</span>
            </button>
            <button class="filter-btn" id="wc1-1v1-btn" data-match-type="1v1">
              <i class="fas fa-user-friends"></i>
              <span>1v1</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content Layout -->
      <div class="ladder-layout">
        <!-- Leaderboard (Main Content) -->
        <section class="leaderboard-section">
          <h2 id="leaderboard-title">Leaderboard</h2>
          <div class="leaderboard-container">
            <table class="leaderboard-table">
              <thead>
                <tr>
                  <th class="rank-header">Rank</th>
                  <th class="player-header">Player</th>
                  <th class="mmr-header">MMR</th>
                  <th class="wins-header">Wins</th>
                  <th class="losses-header">Losses</th>
                  <th class="ratio-header">Win %</th>
                </tr>
              </thead>
              <tbody id="leaderboard-body">
                <tr>
                  <td colspan="6" class="loading-cell">Loading leaderboard data...</td>
                </tr>
              </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination-controls">
              <button id="prev-page" class="btn btn-secondary">Previous</button>
              <span id="page-info">Page 1 of 1</span>
              <button id="next-page" class="btn btn-secondary">Next</button>
            </div>
          </div>
        </section>

        <!-- Ranks Sidebar -->
        <aside class="ranks-sidebar">
          <h3>Ranks</h3>
          <div class="ranks-container" id="ranks-container">
            <div class="loading">Loading ranks...</div>
          </div>
        </aside>

        <!-- Recent Matches Sidebar -->
        <aside class="recent-matches-sidebar">
          <h3>Recent Matches</h3>
          <div class="recent-matches-container" id="recent-matches-container">
            <div class="loading">Loading matches...</div>
          </div>
        </aside>
      </div>

      <!-- Statistics Section -->
      <section class="stats-section">
        <h2>Ladder Statistics</h2>
        <div class="stats-container" id="stats-container">
          <!-- Overview Stats (will be dynamically inserted) -->
          
          <!-- Race Distribution -->
          <div class="stats-card">
            <h3>Race Distribution</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="race-stats-list">
                  <!-- Race list will be dynamically updated based on game type -->
                  <li><span class="stats-name">Human</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">Orc</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">Random</span><span class="stats-value">0</span></li>
                </ul>
              </div>
              <div class="stats-chart">
                <canvas id="race-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Game Modes Distribution -->
          <div class="stats-card">
            <h3>Match Types</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="modes-stats-list">
                  <li><span class="stats-name">1v1</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">2v2</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">3v3</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">4v4</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">FFA</span><span class="stats-value">0</span></li>
                </ul>
              </div>
              <div class="stats-chart">
                <canvas id="match-type-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Rank Distribution -->
          <div class="stats-card">
            <h3>Rank Distribution</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="rank-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- MMR Distribution -->
          <div class="stats-card">
            <h3>MMR Distribution</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="mmr-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Top Maps -->
          <div class="stats-card">
            <h3>Popular Maps</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="maps-stats-list">
                  <li><span class="stats-name">Loading...</span><span class="stats-value">0</span></li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Activity Chart -->
          <div class="stats-card">
            <h3>Recent Activity</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="activity-chart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </main>

  <!-- Report Match Modal -->
  <div id="report-match-modal" class="modal">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="report-modal-title">Report WC II Match</h3>
          <button class="close-modal" title="Close">&times;</button>
        </div>

        <form id="report-match-form">
          <!-- Game Settings -->
          <div class="form-section">
            <h4>Game Settings</h4>
            
            <div class="form-group">
              <label for="match-type">Match Type:</label>
              <select id="match-type" name="matchType" required>
                <option value="1v1">1v1</option>
                <option value="2v2">2v2</option>
                <option value="3v3">3v3</option>
                <option value="4v4">4v4</option>
                <option value="ffa">FFA</option>
                <option value="vsai">Vs AI</option>
              </select>
            </div>

            <div class="form-group">
              <label for="map">Map:</label>
              <input type="text" id="map" name="map" required placeholder="Enter map name">
              <div id="map-suggestions" class="map-suggestions"></div>
            </div>

            <div class="form-group">
              <label for="resource-level">Resource Level:</label>
              <div class="resource-buttons">
                <input type="radio" id="resource-high" name="resourceLevel" value="high" required>
                <label for="resource-high">High</label>
                
                <input type="radio" id="resource-medium" name="resourceLevel" value="medium" required>
                <label for="resource-medium">Medium</label>
                
                <input type="radio" id="resource-low" name="resourceLevel" value="low" required>
                <label for="resource-low">Low</label>
              </div>
            </div>
          </div>

          <!-- Players Section -->
          <div class="form-section">
            <h4>Players</h4>
            <div id="players-container">
              <!-- Player inputs will be generated based on match type -->
            </div>
          </div>

          <!-- Screenshots -->
          <div class="form-group">
            <label for="screenshots">Screenshots (required):</label>
            <input type="file" id="screenshots" name="screenshots" accept=".png" multiple required>
            <small class="help-text">
              PNG format only. Must be taken within the last 2 days and unedited.
            </small>
          </div>

          <!-- Battle Report -->
          <div class="form-group">
            <label for="battle-report">Battle Report:</label>
            <textarea id="battle-report" name="battleReport" rows="3" placeholder="Describe the battle, strategy, key moments, etc..."></textarea>
          </div>

          <!-- YouTube Link -->
          <div class="form-group">
            <label for="youtube-link">YouTube Commentary (Optional):</label>
            <input type="url" id="youtube-link" name="youtubeLink" placeholder="https://youtube.com/watch?v=... or https://youtu.be/...">
            <small class="help-text">Link to a YouTube video commentary of this match</small>
          </div>

          <button type="submit" class="btn btn-primary">Submit Report</button>
        </form>
      </div>
    </div>
  </div>

  <!-- WC1 Report Match Modal -->
  <div id="wc1-report-match-modal" class="modal">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Report WC1 vs AI Match</h3>
          <button class="close-modal" title="Close">&times;</button>
        </div>

        <form id="wc1-report-match-form">
          <!-- Game Settings -->
          <div class="form-section">
            <h4>Match Details</h4>
            
            <!-- Match Type Selection -->
            <div class="form-group">
              <label for="wc1-match-type">Match Type:</label>
              <div class="match-type-buttons">
                <input type="radio" id="wc1-match-type-vsai" name="matchType" value="vsai" checked required>
                <label for="wc1-match-type-vsai" class="match-type-btn">
                  <i class="fas fa-robot"></i>
                  vs AI
                </label>
                
                <input type="radio" id="wc1-match-type-1v1" name="matchType" value="1v1" required>
                <label for="wc1-match-type-1v1" class="match-type-btn">
                  <i class="fas fa-user"></i>
                  1v1
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label for="wc1-map">Scenario Map:</label>
              <select id="wc1-map" name="map" required>
                <option value="">Select a scenario...</option>
                <option value="Random Map">Random Map</option>
                <optgroup label="Forest Campaign">
                  <option value="Forest 1">Forest 1</option>
                  <option value="Forest 2">Forest 2</option>
                  <option value="Forest 3">Forest 3</option>
                  <option value="Forest 4">Forest 4</option>
                  <option value="Forest 5">Forest 5</option>
                  <option value="Forest 6">Forest 6</option>
                  <option value="Forest 7">Forest 7</option>
                </optgroup>
                <optgroup label="Swamp Campaign">
                  <option value="Swamp 1">Swamp 1</option>
                  <option value="Swamp 2">Swamp 2</option>
                  <option value="Swamp 3">Swamp 3</option>
                  <option value="Swamp 4">Swamp 4</option>
                  <option value="Swamp 5">Swamp 5</option>
                  <option value="Swamp 6">Swamp 6</option>
                  <option value="Swamp 7">Swamp 7</option>
                </optgroup>
                <optgroup label="Dungeon Campaign">
                  <option value="Dungeon 1">Dungeon 1</option>
                  <option value="Dungeon 2">Dungeon 2</option>
                  <option value="Dungeon 3">Dungeon 3</option>
                  <option value="Dungeon 4">Dungeon 4</option>
                  <option value="Dungeon 5">Dungeon 5</option>
                  <option value="Dungeon 6">Dungeon 6</option>
                  <option value="Dungeon 7">Dungeon 7</option>
                </optgroup>
              </select>
            </div>

            <div class="form-group">
              <label for="wc1-race">Your Race:</label>
              <div class="race-buttons">
                <input type="radio" id="wc1-human" name="race" value="human" required>
                <label for="wc1-human">
                  <i class="fas fa-shield-alt"></i>
                  Human
                </label>
                
                <input type="radio" id="wc1-orc" name="race" value="orc" required>
                <label for="wc1-orc">
                  <i class="fas fa-hammer"></i>
                  Orc
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="wc1-result">Match Result:</label>
              <div class="result-buttons">
                <input type="radio" id="wc1-win" name="result" value="win" required>
                <label for="wc1-win" class="result-win">
                  <i class="fas fa-trophy"></i>
                  Victory
                </label>
                
                <input type="radio" id="wc1-loss" name="result" value="loss" required>
                <label for="wc1-loss" class="result-loss">
                  <i class="fas fa-skull"></i>
                  Defeat
                </label>
              </div>
            </div>
          </div>

          <!-- Unit Selection Section -->
          <div class="form-section">
            <h4>Army Composition</h4>
            
            <!-- Human Units (shown when Human race selected) -->
            <div id="human-units" class="units-container" style="display: none;">
              <h5><i class="fas fa-shield-alt"></i> Human Units</h5>
              <div class="units-grid">
                <div class="unit-selector">
                  <label for="peasants">
                    <i class="fas fa-hammer"></i>
                    Peasants
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="peasants" name="peasants" min="1" max="6" value="1" class="unit-slider">
                    <span class="unit-count">1</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="footmen">
                    <i class="fas fa-sword"></i>
                    Footmen
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="footmen" name="footmen" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="archers">
                    <i class="fas fa-bow-arrow"></i>
                    Archers
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="archers" name="archers" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="knights">
                    <i class="fas fa-chess-knight"></i>
                    Knights
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="knights" name="knights" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="human-catapults">
                    <i class="fas fa-dot-circle"></i>
                    Catapults
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="human-catapults" name="human-catapults" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="clerics">
                    <i class="fas fa-cross"></i>
                    Clerics
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="clerics" name="clerics" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Orc Units (shown when Orc race selected) -->
            <div id="orc-units" class="units-container" style="display: none;">
              <h5><i class="fas fa-hammer"></i> Orc Units</h5>
              <div class="units-grid">
                <div class="unit-selector">
                  <label for="peons">
                    <i class="fas fa-hammer"></i>
                    Peons
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="peons" name="peons" min="1" max="6" value="1" class="unit-slider">
                    <span class="unit-count">1</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="grunts">
                    <i class="fas fa-fist-raised"></i>
                    Grunts
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="grunts" name="grunts" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="spearmen">
                    <i class="fas fa-spear"></i>
                    Spearmen
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="spearmen" name="spearmen" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="raiders">
                    <i class="fas fa-horse"></i>
                    Raiders
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="raiders" name="raiders" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="orc-catapults">
                    <i class="fas fa-dot-circle"></i>
                    Catapults
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="orc-catapults" name="orc-catapults" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
                
                <div class="unit-selector">
                  <label for="necrolytes">
                    <i class="fas fa-skull"></i>
                    Necrolytes
                  </label>
                  <div class="unit-slider-container">
                    <input type="range" id="necrolytes" name="necrolytes" min="0" max="6" value="0" class="unit-slider">
                    <span class="unit-count">0</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="unit-validation" id="unit-validation" style="display: none;">
              <p class="validation-message"><i class="fas fa-exclamation-triangle"></i> You must have at least one additional unit beyond your worker.</p>
            </div>
          </div>

          <!-- Match Info -->
          <div class="form-section">
            <h4>Additional Details</h4>
            
            <div class="form-group">
              <label for="wc1-duration">Match Duration (optional):</label>
              <input type="number" id="wc1-duration" name="duration" min="1" max="180" placeholder="Minutes">
            </div>

            <div class="form-group">
                          <label for="wc1-battle-report">Battle Report (optional):</label>
            <textarea id="wc1-battle-report" name="battleReport" rows="3" placeholder="Strategy used, difficulty experienced, key moments, etc."></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" id="wc1-cancel-report">Cancel</button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-robot"></i>
              Submit vs AI Match
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Player Stats Modal -->
  <div id="player-stats-modal" class="modal">
    <div class="modal-content">
      <button class="close-modal">&times;</button>
      <div class="modal-header">
        <h2><i class="fas fa-chart-line"></i> <span id="stats-player-name">Player</span> Statistics</h2>
      </div>
      <div class="modal-body">
        <!-- Stats content will be loaded here -->
        <div id="player-stats-content">
          <div class="loading">Loading player statistics...</div>
        </div>
      </div>
    </div>
  </div>

  <div id="footer-container"></div>

  <!-- JavaScript Dependencies -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/modules/ModalManager.js"></script>
      <script type="module" src="/js/modules/ApiClient.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/mmr-calculator.js"></script>
  <script src="/js/rank-animation.js" type="module"></script>
  <script src="/js/modules/ImageCompressor.js" type="module"></script>
  <script src="/js/ultraCompressionIntegration.js" type="module"></script>
  <script src="/js/modules/LadderManager.js" type="module"></script>
  <script src="/js/modules/WC1LadderManager.js" type="module"></script>
  <script src="/js/modules/GameSwitchManager.js" type="module"></script>
  <script src="/js/api-config.js"></script>
  <script type="module" src="/js/app.js"></script>
  <script src="/js/playerDetails.js"></script>
  <script>
    window.addEventListener('load', async () => {
      console.log('🔄 Page fully loaded, initializing ladder...');
      
      // Initialize ladder functionality first
      await initializeLadder();
      
      // Load unified navigation and update profile
      console.log('🔄 Initializing navigation on ladder page...');

      // Load unified navigation
      if (typeof window.loadNavigation === 'function') {
        await window.loadNavigation();
      } else if (typeof window.loadNavbar === 'function') {
        await window.loadNavbar();
      }

      // Update navbar profile
      setTimeout(async () => {
        if (window.updateNavbarProfileUnified) {
          console.log('🔄 Updating navbar profile (unified) on ladder page');
          await window.updateNavbarProfileUnified();
        } else if (window.updateNavbarProfile) {
          console.log('🔄 Updating navbar profile (legacy) on ladder page');
          await window.updateNavbarProfile();
        }
      }, 1000); // 1 second delay after page load
    });
  </script>
  <!-- All JavaScript functionality moved to external modules -->
</body>
</html>
