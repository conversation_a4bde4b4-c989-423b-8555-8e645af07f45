/**
 * Unified Navigation Manager
 * Simplified, clean navigation system that replaces the complex navbar-modern.js
 */

class UnifiedNavigation {
  constructor() {
    this.isInitialized = false;
    this.domElements = {};
    this.currentUser = null;
    this.dropdownStates = new Map();
    
    console.log('🚀 UnifiedNavigation constructor called');
  }

  /**
   * Initialize the navigation system
   */
  async init() {
    if (this.isInitialized) {
      console.log('⚠️ Navigation already initialized, skipping...');
      return true;
    }

    console.log('🔄 Initializing UnifiedNavigation...');
    
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // Cache DOM elements
      if (!this.cacheDOMElements()) {
        console.warn('⚠️ Navigation elements not found, will retry...');
        return false;
      }
      
      // Setup core functionality
      this.setupDropdowns();
      this.setupDocumentClickHandler();
      this.setupNavigation();
      this.setupKeyboardNavigation();
      this.setupLogoutHandling();
      
      // Load and display user data
      await this.loadAndDisplayUser();
      
      this.isInitialized = true;
      console.log('✅ UnifiedNavigation initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to initialize UnifiedNavigation:', error);
      return false;
    }
  }

  /**
   * Cache DOM elements for performance
   */
  cacheDOMElements() {
    const navbar = document.querySelector('.navbar-modern');
    if (!navbar) {
      console.warn('⚠️ Navbar element not found');
      return false;
    }
    
    console.log('🔍 Navbar element found:', !!navbar);
    
    this.domElements = {
      navbar,
      dropdownToggles: navbar.querySelectorAll('.profile-dropdown-toggle, [data-dropdown-toggle]'),
      dropdownMenus: navbar.querySelectorAll('.nav-dropdown-menu'),
      navLinks: navbar.querySelectorAll('.nav-item:not(.profile-dropdown-toggle), .nav-dropdown-item:not(.logout-item), .brand-link'),
      profileImages: {
        desktop: navbar.querySelector('#profile-image'),
        mobile: navbar.querySelector('#profile-image-mobile')
      },
      usernameElements: {
        desktop: navbar.querySelector('#navbar-username'),
        mobile: navbar.querySelector('#navbar-username-mobile')
      },
      logoutItems: navbar.querySelectorAll('.logout-item'),
      mobileMenuToggle: navbar.querySelector('.mobile-menu-toggle'),
      mobileNav: navbar.querySelector('.mobile-nav')
    };

    console.log('📦 Cached elements:', {
      dropdownToggles: this.domElements.dropdownToggles.length,
      dropdownMenus: this.domElements.dropdownMenus.length,
      navLinks: this.domElements.navLinks.length,
      profileImages: !!this.domElements.profileImages.desktop,
      usernameElements: !!this.domElements.usernameElements.desktop
    });
    
    return true;
  }

  /**
   * Setup dropdown functionality
   */
  setupDropdowns() {
    console.log('🔽 Setting up dropdowns...');
    
    this.domElements.dropdownToggles.forEach(toggle => {
      const dropdown = toggle.closest('.nav-dropdown');
      if (!dropdown) return;
      
      const menu = dropdown.querySelector('.nav-dropdown-menu');
      if (!menu) return;
      
      // Store initial state
      this.dropdownStates.set(dropdown, false);
      
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleDropdown(dropdown, toggle, menu);
      });
    });
    
    console.log('✅ Dropdowns setup complete');
  }

  /**
   * Toggle dropdown state
   */
  toggleDropdown(dropdown, toggle, menu) {
    const isOpen = this.dropdownStates.get(dropdown);
    
    // Close all other dropdowns first
    this.closeAllDropdowns();
    
    if (!isOpen) {
      // Open this dropdown
      menu.style.display = 'block';
      toggle.setAttribute('aria-expanded', 'true');
      this.dropdownStates.set(dropdown, true);
      
      // Add animation class
      requestAnimationFrame(() => {
        menu.classList.add('show');
      });
    }
  }

  /**
   * Close all dropdowns
   */
  closeAllDropdowns() {
    this.domElements.dropdownMenus.forEach(menu => {
      menu.style.display = 'none';
      menu.classList.remove('show');
    });
    
    this.domElements.dropdownToggles.forEach(toggle => {
      toggle.setAttribute('aria-expanded', 'false');
    });
    
    // Reset all states
    this.dropdownStates.forEach((value, dropdown) => {
      this.dropdownStates.set(dropdown, false);
    });
  }

  /**
   * Setup document click handler to close dropdowns
   */
  setupDocumentClickHandler() {
    document.addEventListener('click', (e) => {
      // Check if click is outside any dropdown
      const isDropdownClick = e.target.closest('.nav-dropdown');
      if (!isDropdownClick) {
        this.closeAllDropdowns();
      }
    });
  }

  /**
   * Setup navigation link handling
   */
  setupNavigation() {
    console.log('🔗 Setting up navigation...');
    
    this.domElements.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Close dropdowns when navigating
        this.closeAllDropdowns();
        
        // Add active state handling if needed
        this.setActiveNavItem(link);
      });
    });
    
    // Setup mobile menu toggle
    if (this.domElements.mobileMenuToggle && this.domElements.mobileNav) {
      this.domElements.mobileMenuToggle.addEventListener('click', () => {
        this.toggleMobileMenu();
      });
    }
    
    console.log('✅ Navigation setup complete');
  }

  /**
   * Toggle mobile menu
   */
  toggleMobileMenu() {
    const isOpen = this.domElements.mobileNav.classList.contains('show');
    
    if (isOpen) {
      this.domElements.mobileNav.classList.remove('show');
      this.domElements.mobileMenuToggle.setAttribute('aria-expanded', 'false');
    } else {
      this.domElements.mobileNav.classList.add('show');
      this.domElements.mobileMenuToggle.setAttribute('aria-expanded', 'true');
    }
  }

  /**
   * Set active navigation item
   */
  setActiveNavItem(activeLink) {
    // Remove active class from all nav items
    this.domElements.navLinks.forEach(link => {
      link.classList.remove('active');
    });
    
    // Add active class to current item
    activeLink.classList.add('active');
  }

  /**
   * Setup keyboard navigation
   */
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeAllDropdowns();
        
        // Close mobile menu if open
        if (this.domElements.mobileNav?.classList.contains('show')) {
          this.toggleMobileMenu();
        }
      }
    });
  }

  /**
   * Setup logout handling
   */
  setupLogoutHandling() {
    console.log('🚪 Setting up logout handling...');
    
    this.domElements.logoutItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogout();
      });
    });
    
    console.log('✅ Logout handling setup complete');
  }

  /**
   * Handle logout
   */
  handleLogout() {
    console.log('🚪 Logout initiated');
    
    // Check if we're in Electron iframe
    const isInElectronIframe = window !== window.top && 
                               (window.location.search.includes('electron=true') || 
                                window.location.search.includes('electronApp='));
    
    if (isInElectronIframe) {
      console.log('🛡️ Electron iframe logout');
      window.parent.postMessage({
        type: 'ELECTRON_LOGOUT',
        timestamp: Date.now()
      }, '*');
      return;
    }
    
    // Standard web logout
    console.log('🌐 Web browser logout');
    window.location.href = '/auth/logout';
  }

  /**
   * Load and display user data
   */
  async loadAndDisplayUser() {
    console.log('👤 Loading user data...');
    
    try {
      // Check if user data is already available from AuthManager (Electron)
      if (window._appInstance) {
        const authManager = window._appInstance.getComponent('auth');
        if (authManager && authManager.getUser()) {
          const user = authManager.getUser();
          console.log('✅ Using existing user data from AuthManager:', user.username);
          this.updateUserDisplay(user);
          return;
        }
      }
      
      // Fetch user data from API
      const response = await fetch('/api/me', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' }
      });
      
      if (response.ok) {
        const user = await response.json();
        console.log('✅ User data loaded:', user.username);
        this.currentUser = user;
        this.updateUserDisplay(user);
      } else {
        console.log('👤 User not authenticated, showing guest state');
        this.showGuestState();
      }
      
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      this.showGuestState();
    }
  }

  /**
   * Update user display in navigation
   */
  updateUserDisplay(user) {
    console.log('🎨 Updating user display:', user.username);
    
    // Update usernames
    if (this.domElements.usernameElements.desktop) {
      this.domElements.usernameElements.desktop.textContent = user.username || 'User';
    }
    if (this.domElements.usernameElements.mobile) {
      this.domElements.usernameElements.mobile.textContent = user.username || 'User';
    }
    
    // Update profile images
    const avatarUrl = user.avatar || user.profileImage || '/assets/img/default-avatar.svg';
    if (this.domElements.profileImages.desktop) {
      this.domElements.profileImages.desktop.src = avatarUrl;
    }
    if (this.domElements.profileImages.mobile) {
      this.domElements.profileImages.mobile.src = avatarUrl;
    }
    
    console.log('✅ User display updated');
  }

  /**
   * Show guest state
   */
  showGuestState() {
    console.log('👤 Showing guest state');
    
    // Update usernames
    if (this.domElements.usernameElements.desktop) {
      this.domElements.usernameElements.desktop.textContent = 'Guest';
    }
    if (this.domElements.usernameElements.mobile) {
      this.domElements.usernameElements.mobile.textContent = 'Guest';
    }
    
    // Reset profile images
    const defaultAvatar = '/assets/img/default-avatar.svg';
    if (this.domElements.profileImages.desktop) {
      this.domElements.profileImages.desktop.src = defaultAvatar;
    }
    if (this.domElements.profileImages.mobile) {
      this.domElements.profileImages.mobile.src = defaultAvatar;
    }
  }

  /**
   * Refresh user data (called externally when user data changes)
   */
  async refreshUserData() {
    console.log('🔄 Refreshing user data...');
    await this.loadAndDisplayUser();
  }

  /**
   * Update user data (called externally with new user data)
   */
  updateUser(userData) {
    console.log('📝 Updating user data externally');
    this.currentUser = userData;
    this.updateUserDisplay(userData);
  }
}

// Global initialization function
window.initUnifiedNavigation = async function() {
  console.log('🚀 Initializing UnifiedNavigation...');
  
  // Prevent multiple initializations
  if (window.unifiedNavigation && window.unifiedNavigation.isInitialized) {
    console.log('⏳ Navigation already initialized, skipping...');
    return window.unifiedNavigation;
  }
  
  try {
    window.unifiedNavigation = new UnifiedNavigation();
    const success = await window.unifiedNavigation.init();
    
    if (success) {
      console.log('✅ UnifiedNavigation initialized successfully');
      
      // Dispatch ready event
      window.dispatchEvent(new CustomEvent('navigationReady', {
        detail: { navigation: window.unifiedNavigation }
      }));
      
      return window.unifiedNavigation;
    } else {
      console.warn('⚠️ UnifiedNavigation initialization failed');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Error initializing UnifiedNavigation:', error);
    return null;
  }
};

// Auto-initialize if document is ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  window.initUnifiedNavigation();
} else {
  document.addEventListener('DOMContentLoaded', window.initUnifiedNavigation);
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UnifiedNavigation;
}
